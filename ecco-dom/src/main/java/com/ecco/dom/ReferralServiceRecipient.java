package com.ecco.dom;

import com.ecco.dom.contacts.AddressHistory;
import com.ecco.dom.contacts.AddressLike;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;

import org.jspecify.annotations.NonNull;
import javax.persistence.*;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Entity
@DiscriminatorValue(ReferralServiceRecipient.DISCRIMINATOR)
public class ReferralServiceRecipient extends BaseServiceRecipientEvidence {

    private static final long serialVersionUID = 1L;
    public static final String DISCRIMINATOR = "rfrl";
    public static final String PREFIX = "r"; // see BaseServiceRecipient.getPrefix, and service-recipient-dto.ts

    @OneToOne(mappedBy="serviceRecipient", fetch=FetchType.LAZY, cascade = CascadeType.REMOVE)
    private Referral referral;

    @OneToMany(mappedBy = "serviceRecipient", orphanRemoval = true, cascade = CascadeType.REMOVE, fetch = FetchType.EAGER)
    @Fetch(FetchMode.JOIN)
    @OrderBy("id DESC")
    private Set<ServiceRecipientAttachment> attachments = new HashSet<>(); // see https://hibernate.atlassian.net/browse/HHH-9940

    // This works for cascade remove, but could map by parent which has joincolumn updatable/insertable false.
    // See https://stackoverflow.com/questions/2611619/onetomany-and-composite-primary-keys
    @OneToMany(mappedBy = "id.serviceRecipientId", fetch = FetchType.LAZY, cascade = CascadeType.REMOVE)
    private List<ServiceRecipientContact> contacts;

    // Cascade remove address history also
    @OneToMany(mappedBy = "serviceRecipientId", fetch = FetchType.LAZY, cascade = CascadeType.REMOVE)
    private List<AddressHistory> addressHistory;

    // Push this down to BaseServiceRecipient once we can
    @OneToMany(mappedBy = "serviceRecipient", fetch = FetchType.LAZY, cascade = CascadeType.REMOVE)
    private List<CustomEventWithServiceRecipient> events;

    private ClientDetail getClient() {
        ClientDetail client = referral.getClient();
        if (client == null) {
            // Make our NPE more obvious when it occurs (until we have got rid of old referral wizard, which causes it)
            throw new NullPointerException("referral " + referral.getId() + " has no client");
        }
        return client;
    }

    public Set<ServiceRecipientAttachment> getAttachments() {
        return attachments;
    }
    public void setAttachments(Set<ServiceRecipientAttachment> attachments) {
        this.attachments = attachments;
    }

    @Override
    public String getDisplayName() {
        return getClient().getDisplayName();
    }

    @Override
    public AddressLike getAddress() {
        return getClient().getContact().getAddress();
    }

    @Override
    public String getCalendarId() {
        return getClient().getContact().getCalendarId();
    }

    @Override
    public Individual getContact() {
        return getClient().getContact();
    }

    @Override
    public Map<String,String> getTextMap() {
        return referral.getTextMap();
    }

    @Override
    public Referral getTargetEntity() {
        return referral;
    }

    public Referral getReferral() {
        return referral;
    }

    void setReferral(Referral referral) {
        this.referral = referral;
    }

    @Override
    public String getParentCode() {
        return StringUtils.defaultIfEmpty(referral.getCode(), referral.getId().toString());
    }

    @Override
    public Long getParentId() {
        return referral.getId();
    }

    @NonNull
    @Override
    public String getPrefix() {
        return ReferralServiceRecipient.PREFIX;
    }
}
