package com.ecco.dto;

import com.ecco.dom.ProjectAclId;
import com.ecco.dom.ServiceAclId;
import com.ecco.serviceConfig.viewModel.ServiceCategorisationViewModel;
import org.junit.BeforeClass;
import org.junit.Ignore;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;

public class ServicesProjectsDtoTest {

    private static boolean canAccess(ServicesProjectsDto spDto, long serviceId) {
        return spDto.canAccess(serviceId);
    }
    private static boolean canAccess(ServicesProjectsDto spDto, long serviceId, long projectId) {
        return spDto.canAccess(serviceId, projectId);
    }

    @Test
    public void canAccess_allServicesWithNoProjects() {
        var restrictedServices = createRestrictedServices();
        var restrictedProjects = createRestrictedProjects();
        ServicesProjectsDto spDto = new ServicesProjectsDto(restrictedServices, restrictedProjects, svcCats);
        assertTrue(canAccess(spDto, 1, 1));
        assertTrue(canAccess(spDto, 1, 2));
        assertTrue(canAccess(spDto, 1, 3));
        assertTrue(canAccess(spDto, 1, 4));
        assertTrue(canAccess(spDto, 2, 5));
        assertTrue(canAccess(spDto, 2, 6));
        assertTrue(canAccess(spDto, 3, -1));
        assertTrue(canAccess(spDto, 4, -1));
        assertFalse(spDto.hasProjectRestrictions());
        assertTrue(canAccess(spDto, 1));
        assertTrue(canAccess(spDto, 2));
        assertTrue(canAccess(spDto, 3));
        assertTrue(canAccess(spDto, 4));
    }

    @Test
    public void canAccess_allServicesWithUnrestrictedProjects() {
        var restrictedServices = createRestrictedServices();
        // we use -1 historically to indicate 'access all projects'
        // so make sure that is still the case until we do a migration
        var restrictedProjects = createRestrictedProjects(-1);
        ServicesProjectsDto spDto = new ServicesProjectsDto(restrictedServices, restrictedProjects, svcCats);
        assertTrue(canAccess(spDto, 1, 1));
        assertTrue(canAccess(spDto, 1, 2));
        assertTrue(canAccess(spDto, 1, 3));
        assertTrue(canAccess(spDto, 1, 4));
        assertTrue(canAccess(spDto, 2, 5));
        assertTrue(canAccess(spDto, 2, 6));
        assertTrue(canAccess(spDto, 3, -1));
        assertTrue(canAccess(spDto, 4, -1));

        assertTrue(canAccess(spDto, 1));
        assertTrue(canAccess(spDto, 2));
        assertTrue(canAccess(spDto, 3));
        assertTrue(canAccess(spDto, 4));
    }

    @Test
    public void canAccess_notAllServices() {
        // remove service 3
        var restrictedServices = createRestrictedServices().stream()
                .filter(s -> !s.getId().equals(3L)).toList();
        var restrictedProjects = createRestrictedProjects(-1);
        ServicesProjectsDto spDto = new ServicesProjectsDto(restrictedServices, restrictedProjects, svcCats);
        assertTrue(canAccess(spDto, 1, 1));
        assertTrue(canAccess(spDto, 1, 2));
        assertTrue(canAccess(spDto, 1, 3));
        assertTrue(canAccess(spDto, 1, 4));
        assertTrue(canAccess(spDto, 1));

        assertTrue(canAccess(spDto, 2, 5));
        assertTrue(canAccess(spDto, 2, 6));
        assertTrue(canAccess(spDto, 2));

        assertFalse(canAccess(spDto, 3, -1));
        assertFalse(canAccess(spDto, 3));

        assertTrue(canAccess(spDto, 4, -1));
        assertTrue(canAccess(spDto, 4));
    }

    @Test
    public void canAccess_allServicesWithUnrestrictedProjectsAndProjects() {
        var restrictedServices = createRestrictedServices();
        var restrictedProjects = createRestrictedProjects(-1, 2,4);
        ServicesProjectsDto spDto = new ServicesProjectsDto(restrictedServices, restrictedProjects, svcCats);
        // can access all of service 1 because it has no restricted projects to match
        assertTrue(canAccess(spDto, 1, 1));
        assertTrue(canAccess(spDto, 1, 2));
        assertTrue(canAccess(spDto, 1, 3));
        assertTrue(canAccess(spDto, 1, 4));
        assertTrue(canAccess(spDto, 2, 5));
        assertTrue(canAccess(spDto, 2, 6));
        assertTrue(canAccess(spDto, 3, -1));
        assertTrue(canAccess(spDto, 4, -1));
        assertFalse(spDto.hasProjectRestrictions());

        assertTrue(canAccess(spDto, 1));
        assertTrue(canAccess(spDto, 2));
        assertTrue(canAccess(spDto, 3));
        assertTrue(canAccess(spDto, 4));
    }

    @Test
    public void canAccess_notAllProjects() {
        var restrictedServices = createRestrictedServices();
        var restrictedProjects = createRestrictedProjects(2,4,5);
        ServicesProjectsDto spDto = new ServicesProjectsDto(restrictedServices, restrictedProjects, svcCats);
        assertFalse(canAccess(spDto, 1, 1));
        assertTrue(canAccess(spDto, 1, 2));
        assertFalse(canAccess(spDto, 1, 3));
        assertTrue(canAccess(spDto, 1, 4));
        assertTrue(canAccess(spDto, 2, 5));
        assertFalse(canAccess(spDto, 2, 6));
        assertTrue(canAccess(spDto, 3, -1));
        assertTrue(canAccess(spDto, 4, -1));

        assertTrue(canAccess(spDto, 1));
        assertTrue(canAccess(spDto, 2));
        assertTrue(canAccess(spDto, 3));
        assertTrue(canAccess(spDto, 4));
    }

    @Test
    public void canAccess_serviceWithUnrelatedProjects() {
        var restrictedServices = createRestrictedServices();
        var restrictedProjects = createRestrictedProjects(99);
        ServicesProjectsDto spDto = new ServicesProjectsDto(restrictedServices, restrictedProjects, svcCats);
        // can access all of service 1 and 2 because it has no restricted projects to match
        assertTrue(canAccess(spDto, 1, 1));
        assertTrue(canAccess(spDto, 1, 2));
        assertTrue(canAccess(spDto, 1, 3));
        assertTrue(canAccess(spDto, 1, 4));
        assertTrue(canAccess(spDto, 2, 5));
        assertTrue(canAccess(spDto, 2, 6));
        assertTrue(canAccess(spDto, 1, -1));
        assertTrue(canAccess(spDto, 2, -1));
        assertTrue(canAccess(spDto, 3, -1));
        assertTrue(canAccess(spDto, 4, -1));
        assertTrue(canAccess(spDto, 5, -1));

        assertTrue(canAccess(spDto, 1));
        assertTrue(canAccess(spDto, 2));
        assertTrue(canAccess(spDto, 3));
        assertTrue(canAccess(spDto, 4));
    }

    @Test
    public void canAccess_serviceWithSameProjects() {
        var restrictedServices = createRestrictedServices();
        var restrictedProjects = createRestrictedProjects(-1, 6,5);
        ServicesProjectsDto spDto = new ServicesProjectsDto(restrictedServices, restrictedProjects, svcCats);
        assertTrue(canAccess(spDto, 2, 6));
        assertTrue(canAccess(spDto, 2, 5));
        assertFalse(spDto.hasProjectRestrictions());

        assertTrue(canAccess(spDto, 1));
        assertTrue(canAccess(spDto, 2));
        assertTrue(canAccess(spDto, 3));
        assertTrue(canAccess(spDto, 4));
    }

    // test the current shortcomings of the impl
    @Test
    public void highlightRestrictionProblems_2orMoreServices_globalProject() {
        // test with -1 explicit global project with other projects
        // keep services 2, 4, 5 (remove 1, 3)
        var keep = Arrays.stream(new Long[] {2L, 4L, 5L}).toList();
        var restrictedServices = createRestrictedServices().stream()
                .filter(s -> keep.contains(s.getId())).toList();
        // with projects 2
        var restrictedProjects = createRestrictedProjects(-1,2);
        ServicesProjectsDto spDto = new ServicesProjectsDto(restrictedServices, restrictedProjects,svcCats);

        int countProblems = spDto.highlightRestrictionProblems();
        assertEquals(0, countProblems);
        assertFalse(canAccess(spDto, 1, 1));
        assertFalse(canAccess(spDto, 1, 2));
        assertFalse(canAccess(spDto, 1, 3));
        assertFalse(canAccess(spDto, 1, 4));
        // PROBLEM - do we mean access to all service 2
        // currently the code assumes 'all projects' for service 2 since we have no other matching projects
        assertTrue(canAccess(spDto, 2, 5));
        assertTrue(canAccess(spDto, 2, 6));
        assertFalse(canAccess(spDto, 3, -1));
        assertTrue(canAccess(spDto, 4, -1));
        // PROBLEM - do we mean all projects in service 5, or just project 2
        // currently the code obeys 'can access all projects' if its found
        assertTrue(canAccess(spDto, 5, 1));
        assertTrue(canAccess(spDto, 5, 2));
    }

    @Test
    public void highlightRestrictionProblems_2orMoreServices_moreServicesThanProjects() {
        // when more services than projects - then we may be expecting global projects on some service, which it won't be
        // keep services 2, 4, 5 (remove 1, 3)
        var keep = Arrays.stream(new Long[] {2L, 4L, 5L}).toList();
        var restrictedServices = createRestrictedServices().stream()
                .filter(s -> keep.contains(s.getId())).toList();
        // with projects 2
        var restrictedProjects = createRestrictedProjects(2);
        ServicesProjectsDto spDto = new ServicesProjectsDto(restrictedServices, restrictedProjects, svcCats);

        // we expect service 4 not to count because it has no projects
        // so we are expecting 2 services (with projects) and 1 project defined, making a problem
        int countProblems = spDto.highlightRestrictionProblems();
        assertEquals(1, countProblems);
        assertFalse(canAccess(spDto, 1, 1));
        assertFalse(canAccess(spDto, 1, 2));
        assertFalse(canAccess(spDto, 1, 3));
        assertFalse(canAccess(spDto, 1, 4));
        // PROBLEM - we have given access to service 2 without any projects
        // the current code assume we get access to the entire service
        // NB this is not ideal (we should allow access through use of -1 global project)
        // but the expectation from service layer code currently in production (in acl and ldap code) is that acl at service level grants access
        // the downside is that removing a related project means access is given to the whole service
        assertTrue(canAccess(spDto, 2, 5));
        assertTrue(canAccess(spDto, 2, 6));
        assertFalse(canAccess(spDto, 3, -1));
        assertTrue(canAccess(spDto, 4, -1));
        // PROBLEM - we could mean all projects in service 5, or just project 2
        // the code currently matches to the projects
        assertFalse(canAccess(spDto, 5, 1));
        assertTrue(canAccess(spDto, 5, 2));
    }
    @Test
    public void highlightRestrictionProblems_2services_projectOccursOnAnotherService() {
        // test when a project occurs on another service
        // keep services 1, 5 (remove 2, 3, 4)
        var keep = Arrays.stream(new Long[] {1L, 5L}).toList();
        var restrictedServices = createRestrictedServices().stream()
                .filter(s -> keep.contains(s.getId())).toList();
        // with projects 1, 4
        var restrictedProjects = createRestrictedProjects(1, 4);
        ServicesProjectsDto spDto = new ServicesProjectsDto(restrictedServices, restrictedProjects, svcCats);

        // so we are expecting 2 services and a project which belongs to both but might not be intended, making a problem
        int countProblems = spDto.highlightRestrictionProblems();
        assertEquals(1, countProblems);
        // PROBLEM - we may not have intended this access, we may have intended project 4 only on this service
        // the current code restricts according to the project - so if the service has other projects restricted, this could be exposing a project
        // but if no restricted projects are found for the service, then this could be limiting it to this project
        assertTrue(canAccess(spDto, 1, 1));
        assertFalse(canAccess(spDto, 1, 2));
        assertFalse(canAccess(spDto, 1, 3));
        assertTrue(canAccess(spDto, 1, 4));
        assertFalse(canAccess(spDto, 2, 5));
        assertFalse(canAccess(spDto, 2, 6));
        assertFalse(canAccess(spDto, 3, -1));
        assertFalse(canAccess(spDto, 4, -1));
        assertTrue(canAccess(spDto, 5, 1));
        assertFalse(canAccess(spDto, 5, 2));
    }

    // test the improvement, which is that multiple services can have different projects
    @Test
    public void canAccess_differentProjectsInDifferentServices() {
        // test when a project occurs on another service
        // keep services 2, 5 (remove 1, 3, 4)
        var keep = Arrays.stream(new Long[] {2L, 5L}).toList();
        var restrictedServices = createRestrictedServices().stream()
                .filter(s -> keep.contains(s.getId())).toList();
        // with projects 6, 1
        var restrictedProjects = createRestrictedProjects(1, 6);
        ServicesProjectsDto spDto = new ServicesProjectsDto(restrictedServices, restrictedProjects, svcCats);

        int countProblems = spDto.highlightRestrictionProblems();
        assertEquals(0, countProblems);
        assertFalse(canAccess(spDto, 1, 1));
        assertFalse(canAccess(spDto, 1, 2));
        assertFalse(canAccess(spDto, 1, 3));
        assertFalse(canAccess(spDto, 1, 4));
        assertFalse(canAccess(spDto, 2, 5));
        assertTrue(canAccess(spDto, 2, 6));
        assertFalse(canAccess(spDto, 3, -1));
        assertFalse(canAccess(spDto, 4, -1));
        assertTrue(canAccess(spDto, 5, 1));
        assertFalse(canAccess(spDto, 5, 2));
    }

    // ECCO-420 this test highlights the problem
    // specifically (given that services and projects are given) we test for service1 with project1 and service5 with project2
    // which needs therefore to exclude service5 and project1 - by global definitions this would pass ordinarily
    @Test
    @Ignore
    public void canAccess_projectsInDifferentServices() {
        // remove service 2
        var restrictedServices = createRestrictedServices().stream()
                .filter(s -> !s.getId().equals(2L)).toList();
        var restrictedProjects = createRestrictedProjects(1, 2);
        ServicesProjectsDto spDto = new ServicesProjectsDto(restrictedServices, restrictedProjects, svcCats);
        assertTrue(canAccess(spDto, 1, 1));
        assertFalse(canAccess(spDto, 1, 2)); // See comments above - this is not supported without service/project combo acls
        assertFalse(canAccess(spDto, 1, 3));
        assertFalse(canAccess(spDto, 1, 4));
        assertFalse(canAccess(spDto, 2, 5));
        assertFalse(canAccess(spDto, 2, 6));
        assertFalse(canAccess(spDto, 3, -1)); // FIXME: service 3 has no projects, so returns true which makes sense
        assertFalse(canAccess(spDto, 4, -1)); // FIXME: service 4 has no projects, so returns true which makes sense
        assertFalse(canAccess(spDto, 5, 1)); // See comments above - this is not supported without service/project combo acls
        assertTrue(canAccess(spDto, 5, 2));
    }

    private static List<ServiceCategorisationViewModel> svcCats = new ArrayList<>();
    @BeforeClass
    public static void createServices() {
        createServiceWithProjects(1, 2, 1, 4, 3);
        createServiceWithProjects(2, 6, 5);
        createServiceWithProjects(3);
        createServiceWithProjects(4);
        createServiceWithProjects(5, 1, 2);
    }
    private static void createServiceWithProjects(long id, Integer... projects) {
        if (projects.length == 0) {
            var sc = new ServiceCategorisationViewModel((int) id*-1, id, null);
            svcCats.add(sc);
        }
        for (var pid : projects) {
            var sc = new ServiceCategorisationViewModel(Long.valueOf((id+10)*pid).intValue(), id, pid.longValue());
            svcCats.add(sc);
        }
    }
    private List<ServiceAclId> createRestrictedServices() {
        return svcCats.stream()
                .map(ServiceCategorisationViewModel::getServiceId).distinct()
                .map(id -> new ServiceAclId(id, "service-" + id)).toList();
    }
    private List<ProjectAclId> createRestrictedProjects(Integer... projectIds) {
        List<ProjectAclId> projects = new ArrayList<>();
        if (projectIds != null) {
            for (var pid : projectIds) {
                var p = new ProjectAclId(pid.longValue(), "project-" + pid);
                projects.add(p);
            }
        }
        return projects;
    }

}
