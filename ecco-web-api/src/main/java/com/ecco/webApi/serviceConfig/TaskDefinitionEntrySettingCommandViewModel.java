package com.ecco.webApi.serviceConfig;

import com.ecco.dto.ChangeViewModel;

import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;

import org.springframework.web.util.UriComponentsBuilder;

public class TaskDefinitionEntrySettingCommandViewModel extends ServiceTypeCommandViewModel {

    @NonNull
    public String taskName;
    @NonNull
    public String settingName;
    @Nullable
    public ChangeViewModel<String> valueChange;

    /** For Jackson etc */
    @Deprecated
    TaskDefinitionEntrySettingCommandViewModel() {
        super();
    }

    public TaskDefinitionEntrySettingCommandViewModel(long serviceTypeId, @NonNull String taskName, @NonNull String settingName) {
        super(UriComponentsBuilder
                .fromUriString("service-config/{serviceTypeId}/task/{taskName}/setting/{settingName}/")
                .buildAndExpand(serviceTypeId, taskName, settingName).toUriString(), serviceTypeId);
        this.taskName = taskName;
        this.settingName = settingName;
    }

    public boolean hasChanges() {
        return valueChange != null;
    }

}