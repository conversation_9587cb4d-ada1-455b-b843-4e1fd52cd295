package com.ecco.webApi.controllers;

import com.ecco.dom.commands.DeleteRequestEvidenceCommand;
import com.ecco.infrastructure.hibernate.EntityUriMapper;
import com.ecco.infrastructure.hibernate.EntityUriMapperImpl;

import org.jspecify.annotations.NonNull;
import java.io.Serializable;
import java.net.URI;


public class EntityDbUriMapperImpl extends EntityUriMapperImpl implements EntityUriMapper, Serializable {
    private static final long serialVersionUID = 1L;

    @Override
    @NonNull
    public Class<?> entityClassForUri(URI uri) {
        final String entityName = entityNameForUri(uri);
        Class<?> entityClass;

        switch (entityName) {
            case "sr-"+DeleteRequestEvidenceCommand.DISCRIMINATOR: {
                entityClass = DeleteRequestEvidenceCommand.class;
                break;
            }
            default:
                throw new IllegalArgumentException("No entity class could be found for URI " + String.valueOf(uri));
        }
        return entityClass;
    }

    @NonNull
    public String stringIdForUri(URI uri) {
        if (uri == null || !uri.getScheme().equals("entity") || !uri.getPath().startsWith("/")) {
            throw new IllegalArgumentException("URI needs to be " + ENTITY_SCHEME + "://<name>/<id> (was " + String.valueOf(uri) + ")");
        }
        return uri.getPath().substring(1);
    }

}
