package com.ecco.webApi.calendar;

import com.ecco.webApi.evidence.BaseCommandViewModel;
import org.jspecify.annotations.NonNull;

import java.util.UUID;

public interface CalendarEntryCommandViewModelParent {

    @NonNull
    UUID getCommandUuid();

    @NonNull
    CalendarEntryCommandViewModel getCalendarEntryViewModel();

    @NonNull
    BaseCommandViewModel getCalendarEntryViewModelParent();

    void setCalendarEntryViewModel(@NonNull CalendarEntryCommandViewModel entryVM);
}
