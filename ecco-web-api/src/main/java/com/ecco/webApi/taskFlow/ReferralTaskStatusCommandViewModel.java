package com.ecco.webApi.taskFlow;

import static lombok.AccessLevel.PACKAGE;

import com.ecco.dto.ChangeViewModel;
import lombok.NoArgsConstructor;

import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;

import java.time.LocalDateTime;
import java.util.UUID;

@NoArgsConstructor(access = PACKAGE)
public class ReferralTaskStatusCommandViewModel extends ServiceRecipientTaskCommandViewModel {

    public static final String TASK_NAME = "taskInstance";

    @NonNull
    public String operation;

    @Nullable
    public ChangeViewModel<LocalDateTime> dueDate;

    /**
     * What the task is about - for ad-hoc
     */
    @Nullable
    public ChangeViewModel<String> description;

    @Nullable
    public ChangeViewModel<LocalDateTime> completed;

    @Nullable
    public ChangeViewModel<String> assignee; // username, to work with activiti and linear

    @Nullable
    public ChangeViewModel<Integer> relevantGroup;


    public ReferralTaskStatusCommandViewModel(@NonNull String operation, @NonNull String taskHandle, int serviceRecipientId) {
        super(serviceRecipientId, TASK_NAME, taskHandle);
        this.operation = operation;
    }

    public ReferralTaskStatusCommandViewModel(@NonNull UUID taskInstanceUuid, @NonNull String operation, int serviceRecipientId) {
        super(serviceRecipientId, TASK_NAME, null); // taskHandle could possibly be populated or migrate to taskInstanceId within the Task.Handle so that this command would work for Activiti too
        this.taskInstanceId = taskInstanceUuid.toString();
        this.operation = operation;
    }
}
