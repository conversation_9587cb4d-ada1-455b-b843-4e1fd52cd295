package com.ecco.webApi.taskFlow;

import com.ecco.dao.commands.ServiceRecipientCommandRepository;
import com.ecco.dom.ClientDetailAbstract;
import com.ecco.dom.ReferralServiceRecipient;
import com.ecco.dom.hr.WorkerJobServiceRecipient;
import com.ecco.evidence.repositories.ServiceRecipientRepository;
import com.ecco.webApi.CommandResult;
import com.ecco.webApi.controllers.WorkflowTaskController;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jspecify.annotations.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

@Component
public class ReferralTaskEditEmergencyDetailsCommandHandler extends ServiceRecipientTaskCommandHandler<ReferralTaskEditEmergencyDetailsCommandViewModel> {

    @NonNull
    private final ServiceRecipientRepository serviceRecipientRepository;

    @Autowired
    public ReferralTaskEditEmergencyDetailsCommandHandler(ObjectMapper objectMapper,
                                                          @NonNull WorkflowTaskController workflowTaskController,
            ServiceRecipientCommandRepository serviceRecipientCommandRepository,
            ServiceRecipientRepository serviceRecipientRepository) {
        super(objectMapper, workflowTaskController, serviceRecipientCommandRepository, ReferralTaskEditEmergencyDetailsCommandViewModel.class);

        this.serviceRecipientRepository = serviceRecipientRepository;
    }

    @Override
    protected CommandResult handleTaskInternal(Authentication auth, @NonNull ServiceRecipientTaskParams params,
                                               ReferralTaskEditEmergencyDetailsCommandViewModel vm) {
        var serviceRecipient = serviceRecipientRepository.findOne(params.serviceRecipientId);

        ClientDetailAbstract c = serviceRecipient instanceof ReferralServiceRecipient
                ? ((ReferralServiceRecipient) serviceRecipient).getReferral().getClient()
                : ((WorkerJobServiceRecipient)serviceRecipient).getWorkerJob().getWorker();

        if (vm.communicationNeeds != null) {
            c.setCommunicationNeeds(vm.communicationNeeds.to);
        }

        if (vm.dentistDetails != null) {
            c.setDentistDetails(vm.dentistDetails.to);
        }

        if (vm.descriptionDetails != null) {
            c.setDescriptionDetails(vm.descriptionDetails.to);
        }

        if (vm.doctorDetails != null) {
            c.setDoctorDetails(vm.doctorDetails.to);
        }

        if (vm.emergencyDetails != null) {
            c.setEmergencyDetails(vm.emergencyDetails.to);
        }

        if (vm.emergencyKeyword != null) {
            c.setEmergencyKeyword(vm.emergencyKeyword.to);
        }

        if (vm.risksAndConcerns != null) {
            c.setRisksAndConcerns(vm.risksAndConcerns.to);
        }

        if (vm.medicationDetails != null) {
            c.setMedicationDetails(vm.medicationDetails.to);
        }
        return null;
    }
}
