import {DefaultCryptoService} from "@eccosolutions/ecco-crypto";
import {applicationRootPath} from "application-properties";
import {Credentials, formLogin, getGlobalApiClient} from "ecco-dto";
import {UserSessionManager} from "./UserSessionManager";
import {Database, isSupported} from "./db/indexed-db";
import {OfflineSchema} from "./db/OfflineSchema";
import {OfflineSecurityDtoRepository} from "./security/OfflineSecurityDtoRepository";
import {SecurityRepository} from "./security/SecurityRepository";
import {UserSessionRepository} from "./UserSessionRepository";
import {SessionStorageUserSessionRepository} from "./SessionStorageUserSessionRepository";

export const formThenOfflineLogin = (baseWebApiUrl: string, credentials: Credentials): Promise<void> => {
    let login = () => formLogin(baseWebApiUrl, credentials);
    const sessionManager = getUserSessionManager();
    if (sessionManager) {
        return sessionManager.login(credentials.username, credentials.password)
            .finally(login)
            .then(() => {/* for Promise<void> */});
    }
    else {
        return login();
    }
}

let userSessionManager: UserSessionManager | null;

export function getUserSessionManager(): UserSessionManager | null {

// If indexedDb is supported then we need repositories for falling back to offline use
    if (!userSessionManager && isSupported()) {

        const contextPath = applicationRootPath.startsWith("http") ? new URL(applicationRootPath).pathname : applicationRootPath;
        const databaseName = "ecco-offline-" + contextPath.replace(/\//g, "-");
        const database = Database.open(new OfflineSchema(databaseName));

        const cryptoService = new DefaultCryptoService();
        const offlineSecurityDtoRepository = new OfflineSecurityDtoRepository(database);
        const securityRepository = new SecurityRepository(offlineSecurityDtoRepository);
        const userSessionRepository: UserSessionRepository = new SessionStorageUserSessionRepository();
        userSessionManager = new UserSessionManager(getGlobalApiClient(), cryptoService, securityRepository, userSessionRepository);
    }
    return userSessionManager;
}
