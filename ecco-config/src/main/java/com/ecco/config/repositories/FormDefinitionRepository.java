package com.ecco.config.repositories;


import com.ecco.config.dom.FormDefinition;
import com.ecco.infrastructure.spring.data.CrudRepositoryWithFindOne;

import java.util.UUID;
import java.util.stream.Stream;

/**
 * CRUD Persitence for form definitions
 */
public interface FormDefinitionRepository extends CrudRepositoryWithFindOne<FormDefinition, UUID> {

    Stream<FormDefinition> findAllByOrderByOrderbyAsc();

}
