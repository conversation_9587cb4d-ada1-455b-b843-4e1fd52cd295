<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.eccosolutions</groupId>
        <artifactId>parent</artifactId>
        <version>1.0.0.CI-SNAPSHOT</version>
        <relativePath>../parent/pom.xml</relativePath>
    </parent>

    <groupId>ecco</groupId>
    <artifactId>ecco-reports-clg</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>

    <dependencies>

        <dependency>
            <groupId>ecco</groupId>
            <artifactId>ecco-reports</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!-- abstract excel handling -->
        <!--
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
            <version>${springCoreVersion}</version>
        </dependency>
        -->

        <!-- excel -->
        <!--
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>${poiVersion}</version>
        </dependency>
        -->

    </dependencies>

</project>
