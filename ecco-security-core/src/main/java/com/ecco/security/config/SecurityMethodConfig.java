package com.ecco.security.config;

import com.ecco.security.acl.CustomPermissionEvaluator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;

/**
 * Replacement for applicationContext-security-method.xml.
 *
 * [applicationContext-security-method.xml] is included from each servlet's context, since a servlet's 'child' context can see the parent
 * context (which is available to all servlets and loaded by ContextLoaderListener in web.xml) but the parent
 * context can't see the child servlet context beans, so the security annotations get silently ignored.
 * By specifying the security on the servlet context it allows access to all beans.
 * See http://docs.spring.io/spring-security/site/faq/faq.html#faq-method-security-in-web-context 3.2.
 * The global-method-security causes GlobalMethodSecurityBeanDefinitionParser to create various beans
 * and ultimately InfrastructureAdvisorAutoProxyCreator, which applies the pointcut only to beans in it's own
 * beanfactory.
*/

@Configuration
@EnableGlobalMethodSecurity(
        prePostEnabled = true,
        securedEnabled = true)
public class SecurityMethodConfig {

    @Bean
    public CustomPermissionEvaluator customPermissionEvaluator() {
        return new CustomPermissionEvaluator();
    }

    @Bean
    @Primary
    public DefaultMethodSecurityExpressionHandler expressionHandler(CustomPermissionEvaluator customPermissionEvaluator) {
        var handler = new DefaultMethodSecurityExpressionHandler();
        handler.setPermissionEvaluator(customPermissionEvaluator);
        return handler;
    }

}
