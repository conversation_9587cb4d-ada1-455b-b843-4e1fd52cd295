package com.ecco.security.service;

import com.ecco.dom.Identified;
import com.ecco.dom.Individual;
import com.ecco.security.dom.LdapGroupMapping;
import com.ecco.security.dom.User;
import com.ecco.security.repositories.LdapGroupMappingRepository;
import com.ecco.security.dto.AclEntryDto;
import com.ecco.security.dto.AclExtractor;
import com.ecco.security.acl.AclHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.acls.domain.PrincipalSid;
import org.springframework.security.acls.model.Permission;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UsernameNotFoundException;

import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.function.Function;

/**
 * An important class used to get the roles and permissions of a user and apply them to ecco roles and permissions.
 * This is used by both ActiveDirectory authentication and Kerberos authentication.
 */
@RequiredArgsConstructor
@Slf4j
public class EccoUserDetailsContextMapper {

    private final LdapGroupMappingRepository groupMappingRepository;
    private final UserManagementService userManagementService;
    private final Boolean enableAcls;
    private final AclHandler aclHandler;

    public User findOrCreateUser(String username, boolean enabled, String oAuthId, String contactName,
                                 Collection<? extends GrantedAuthority> authorities,
                                 Function<String, String> groupMapper) {
        User user;
        try {
            user = userManagementService.loadUserByUsername(username);
            if (user.getOAuthId() == null) {
                user.setOAuthId(oAuthId);
            }
        } catch (UsernameNotFoundException e) {
            user = createUser(username, enabled, oAuthId, contactName);
        }
        List<LdapGroupMapping> mappings = groupMappingRepository.findAllByOrderByLdapGroupAscLocalGroupAscLocalClassAscLocalIdAsc();
        user = mapAuthoritiesToLocalGroups(mappings, user, authorities, groupMapper);
        if (enableAcls)
            // ldap 'authorities' are currently used to map acls
            mapAuthoritiesToAcls(mappings, user, authorities, groupMapper);
        return user;
    }

    private User mapAuthoritiesToLocalGroups(List<LdapGroupMapping> mappings, User user, Collection<? extends GrantedAuthority> authorities,
                                             Function<String, String> groupMapper) {
        log.info("mapAuthoritiesToLocalGroups: mapping ldap/oicd authorities to local ecco groups for user: " + user.getUsername());
        Set<String> targetGroups = deriveTargetGroups(authorities, mappings, groupMapper);
        log.info("mapAuthoritiesToLocalGroups: matched groups: " + targetGroups);
        user = userManagementService.mergeNewGroups(user, targetGroups);
        return user;
    }

    private Set<String> deriveTargetGroups(Collection<? extends GrantedAuthority> authorities, List<LdapGroupMapping> mappings,
                                           Function<String, String> groupMapper) {
        Set<String> targetGroups = new HashSet<>();
        for (GrantedAuthority inAuthority : authorities) {
            String inAuthorityName = groupMapper.apply(inAuthority.getAuthority());
            log.debug("deriveTargetGroups: finding ecco ldapGroups against incoming authority: " + inAuthorityName + " [from " + inAuthority.getAuthority() + "]");
            for (LdapGroupMapping mapping : mappings) {
                if (mapping.getLocalGroup() != null) {
                    log.debug("deriveTargetGroups: looping ecco ldapGroup: " + mapping.getLdapGroup());
                    if (mapping.getLdapGroup().equalsIgnoreCase(inAuthorityName)) {
                        log.debug("deriveTargetGroups: MATCHED incoming authority (" + inAuthorityName + "); adding ecco group (" + mapping.getLocalGroup().getName() + ")");
                        targetGroups.add(mapping.getLocalGroup().getName());
                    }
                    // special case - ignore ldap source and simply check for a * mapping
                    if ("*".equals(mapping.getLdapGroup())) {
                        log.debug("deriveTargetGroups: MATCHED *; adding ecco group (" + mapping.getLocalGroup().getName() + ")");
                        targetGroups.add(mapping.getLocalGroup().getName());
                    }
                }
            }
        }
        return targetGroups;
    }

    private void mapAuthoritiesToAcls(List<LdapGroupMapping> mappings, User user, Collection<? extends GrantedAuthority> authorities,
                                      Function<String, String> groupMapper) {
        log.info("mapAuthoritiesToAcls: mapping ldap authorities to local ecco ACLs (as per 'memberOf attribute values:') for user: " + user.getUsername());
        Set<AclEntryDto> targetAcls = deriveTargetAcls(user.getUsername(), authorities, mappings, groupMapper);
        log.info("mapAuthoritiesToAcls: matched acls: " + targetAcls);

        Collection<AclExtractor<? extends Identified<Long>>> aclExtractors = aclHandler.aclExtractors();
        aclHandler.updateAclEntryDifferences(new PrincipalSid(user.getUsername()), aclExtractors, targetAcls);
    }

    private Set<AclEntryDto> deriveTargetAcls(String username, Collection<? extends GrantedAuthority> authorities, List<LdapGroupMapping> mappings,
                                              Function<String, String> groupMapper) {
        Set<AclEntryDto> targetAcls = new HashSet<>();
        for (GrantedAuthority inAuthority : authorities) {
            String inAuthorityName = groupMapper.apply(inAuthority.getAuthority());
            log.debug("deriveTargetAcls: finding ecco ldapGroups against incoming authority: " + inAuthorityName + " [from " + inAuthority.getAuthority() + "]");
            for (LdapGroupMapping mapping : mappings) {
                if (StringUtils.isNotEmpty(mapping.getLocalClass())) {
                    log.debug("deriveTargetAcls: looping ecco ldapGroup: " + mapping.getLdapGroup());
                    if (mapping.getLdapGroup().equalsIgnoreCase(inAuthorityName)) {
                        log.debug("deriveTargetAcls: MATCHED incoming authority (" + inAuthorityName + "); adding ecco ACL (" + mapping.getLocalClass() + ": " + mapping.getLocalId() + ")");
                        AclEntryDto ae = createAclEntry(username, mapping.getLocalClass(), mapping.getLocalId());
                        if (ae != null)
                            targetAcls.add(ae);
                    }
                    // special case - ignore incoming ldap groups and check for wildcard in ecco to map anything from ldap
                    if ("*".equals(mapping.getLdapGroup())) {
                        log.debug("deriveTargetAcls: MATCHED *; adding ecco ACL (" + mapping.getLocalClass() + ": " + mapping.getLocalId() + ")");
                        AclEntryDto ae = createAclEntry(username, mapping.getLocalClass(), mapping.getLocalId());
                        if (ae != null)
                            targetAcls.add(ae);
                    }
                }
            }
        }
        return targetAcls;
    }

    private AclEntryDto createAclEntry(String username, String clazz, long id) {
        Class c;
        try {
            c = Class.forName(clazz);
        } catch (ClassNotFoundException e1) {
            log.error("ACL class NOT found (acl not applied to user): " + clazz, e1);
            return null;
        }
        // read is assumed, since currently our acl usage is to get a list of filtered services/projects to use in queries, not on the objects themselves
        // so we just want the list, and read permission is what we use to allow access
        Permission p = org.springframework.security.acls.domain.BasePermission.READ;
        return new AclEntryDto(username, id, c, p);
    }

    private User createUser(String username, boolean enabled, String oAuthId, String contactName) {
        log.info("Creating new ecco user for " + username);
        User user = UserManagementServiceImpl.createDefaultUser();
        user.setActiveDirectoryUser(true);
        user.setUsername(username);
        // ldapUserDetails MIGHT contain the password - if its passed from AD/LDAP
        // (see ActiveDirectoryLdapAuthenticationProvider.setUseAuthenticationRequestCredentials)
        // however, its safer to assume this isn't likely and set one ourselves
        // this needs to be not null, but empty is bad news too as after a successful login, blank can then be entered!
        // so we re-use what we already use for clients
        user.setNewPassword(userManagementService.createPassword());
        user.setEnabled(enabled);
        user.setOAuthId(oAuthId);

        log.info("Creating new ecco user with contact name: " + contactName);
        Individual person = new Individual();
        // first check the assumption that the cn= has a first and last name
        // this could be extended to be a regex in the database
        if (StringUtils.contains(contactName, " ")) {
            // use StringUtils to be safe given we don't know the format of this string
            person.setFirstName(StringUtils.substringAfterLast(contactName, " "));
            person.setLastName(StringUtils.substringBeforeLast(contactName, " "));
        } else {
            // we set something - otherwise we get blank in drop down lists
            person.setFirstName(contactName);
        }
        // this is now specified in userManagementService for cosmo which requires an email
        //person.setEmail(ldapUserDetails.getUsername() + "@eccosolutions.co.uk");
        user.setContact(person);
        userManagementService.createUser(user);
        return userManagementService.loadUserByUsername(user.getUsername());
    }

}
