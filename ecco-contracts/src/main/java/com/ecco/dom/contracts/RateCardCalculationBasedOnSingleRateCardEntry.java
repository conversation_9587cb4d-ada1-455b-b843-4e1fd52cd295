package com.ecco.dom.contracts;

import com.google.common.collect.Range;
import kotlin.Pair;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.Instant;
import java.time.ZonedDateTime;
import java.util.*;

/**
 * Default implementation for choosing RateCard entries to charge by, and the calculation.
 * This assumes there is nothing smaller than a minute to charge by.
 */
@RequiredArgsConstructor
@Slf4j
public class RateCardCalculationBasedOnSingleRateCardEntry extends RateCardCalculationBase implements RateCardCalculation {

    @Override
    public Optional<RateCard> getRateCardInDate(@NonNull Instant instant, @NonNull List<RateCard> rateCards) {
        return getRateCardInDate(instant, rateCards, false);
    }

    @Override
    public List<RateCard> getRateCardsInDateRange(@NonNull Range<Instant> range, @NonNull List<RateCard> rateCards) {
        return super.getRateCardsInDateRange(range, rateCards);
    }

    @Override
    public List<RateCardEntry> determineRateCardEntries(@NonNull RateCard rateCard, @Nullable Integer matchCategoryTypeId,
                                                        @Nullable Integer matchChargeCategoryId, @Nullable Collection<String> matchFactors) {

        return determineRateCardEntries(rateCard, matchCategoryTypeId, matchChargeCategoryId, matchFactors, true);
    }

    /**
     * Calculate the charge based on the rate card entry.
     * @param entries The rate card entries to use for calculation
     * @param dateTimeRange The date-time range to calculate charges for, allowing for calendar month calculations
     */
    @Override
    public BigDecimal calculateCharge(@NonNull List<RateCardEntry> entries, @NonNull Range<ZonedDateTime> dateTimeRange) {
        // Calculate the duration in minutes between the lower and upper bounds of the range
        ZonedDateTime start = dateTimeRange.lowerEndpoint();
        ZonedDateTime end = dateTimeRange.upperEndpoint();
        int minutes = (int) Duration.between(start, end).toMinutes();

        // Use the existing calculation method with the calculated minutes
        return this.calculateChargeFromSingleEntry(entries.get(0), new Pair<>(BigDecimal.ZERO, minutes)).component1();
    }

    private Pair<BigDecimal, Integer> calculateChargeFromSingleEntry(@NonNull RateCardEntry entry, Pair<BigDecimal, Integer> chargeAndRemainingTime) {
        var entryChargeAndRemainingTime = this.calculateOneCharge(chargeAndRemainingTime, false, entry);
        var cumulativeChargeAndRemainingTime = new Pair<>(chargeAndRemainingTime.component1().add(entryChargeAndRemainingTime.component1()), entryChargeAndRemainingTime.component2());

        return entry.getChildRateCardEntry() != null
            ? this.calculateChargeFromSingleEntry(entry.getChildRateCardEntry(), cumulativeChargeAndRemainingTime)
            : cumulativeChargeAndRemainingTime;
    }

}
