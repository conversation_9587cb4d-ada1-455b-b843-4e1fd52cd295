package com.ecco.contracts.ratecards

import com.ecco.dom.contracts.RateCardEntry
import com.google.common.collect.Range
import java.math.BigDecimal
import java.time.ZonedDateTime

class MonthRateCardEntryHandler : RateCardEntryHandler {
    override fun handle(rateCardEntry: RateCardEntry, chargePeriod: Range<ZonedDateTime>): Pair<BigDecimal, Range<ZonedDateTime>> {
        // Calculate the number of months between the lower and upper bounds of the range including accounting for years
        // and adjust for non-full months e.g. 20 Mar - 10 May = 1 months not two
        val months = (
            (chargePeriod.upperEndpoint().year - chargePeriod.lowerEndpoint().year) * 12L +
                chargePeriod.upperEndpoint().monthValue - chargePeriod.lowerEndpoint().monthValue -
                if (chargePeriod.upperEndpoint().dayOfMonth < chargePeriod.lowerEndpoint().dayOfMonth) 1 else 0
            )
        val charge = rateCardEntry.unitCharge!!.multiply(BigDecimal(months))

        val newLowerEndpoint = chargePeriod.lowerEndpoint().plusMonths(months)
        // Return the charge with the range adjusted to reflect the full months we've charged for
        return Pair(charge, Range.closedOpen(newLowerEndpoint, chargePeriod.upperEndpoint()))
    }
}