package com.ecco.exceptions;

/**
 * All application exceptions should subclass this class.
 */

// we create a checked Exception where we can roll back a transaction - in addition to unchecked exceptions and errors
// see - http://static.springframework.org/spring/docs/2.0.x/reference/transaction.html#transaction-declarative-rolling-back
public abstract class ApplicationException extends Exception {

    /**
     * Constructor for ApplicationException.
     * @param message The message of the exception
     */
    public ApplicationException(String message) {
        super(message);
    }

    /**
     * Constructor for ApplicationException.
     * @param message The message of the exception.
     * @param cause The cause of the exception.
     */
    public ApplicationException(String message, Throwable cause) {
        super(message, cause);
    }

}
