<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd"
    logicalFilePath="2023/evidence-domain">

    <!-- HANDLES: (based on search for <createTable)
     - see main evidenceDomainChangeLog.xml for what tables are involved in the domain
    -->

    <!--
    NB supportplanflags was created in evidenceDomainChangeLog, but this is the only table
    wholly created in evidence liquibase. Other evidence files have sql which means using the
    evidence files context alone would fail, but since the 'onwards' files run in order 1.1,
    1.2, 2021 etc - its within each year the order is evidence first then general - it means
    that we can continue to refer to evidence tables created in earlier general changelogs in this file.
    -->
    <changeSet id="rename-evdnc" author="adamjhamer">
        <renameTable oldTableName="supporthractions" newTableName="evdnc_hractions"/>
        <renameTable oldTableName="supporthrcomments" newTableName="evdnc_hrcomments"/>
        <renameTable oldTableName="supporthroutcomes" newTableName="evdnc_hroutcomes"/>
        <renameTable oldTableName="supporthrwork" newTableName="evdnc_hrwork"/>

        <renameTable oldTableName="supportplanactions" newTableName="evdnc_supportactions"/>
        <renameTable oldTableName="supportplancomments" newTableName="evdnc_supportcomments"/>
        <renameTable oldTableName="supportplanoutcomes" newTableName="evdnc_supportoutcomes"/>
        <renameTable oldTableName="supportplanwork" newTableName="evdnc_supportwork"/>
        <renameTable oldTableName="supportplananswers" newTableName="evdnc_supportanswers"/>
        <renameTable oldTableName="supportplanflags" newTableName="evdnc_supportflags"/>

        <renameTable oldTableName="supportthreatactions" newTableName="evdnc_threatactions"/>
        <renameTable oldTableName="supportthreatcomments" newTableName="evdnc_threatcomments"/>
        <renameTable oldTableName="supportthreatoutcomes" newTableName="evdnc_threatoutcomes"/>
        <renameTable oldTableName="supportthreatwork" newTableName="evdnc_threatwork"/>
        <renameTable oldTableName="supportthreatflags" newTableName="evdnc_threatflags"/>
    </changeSet>
    <changeSet id="rename-evdnc-more" author="adamjhamer">
        <renameTable oldTableName="supportplanwork_actions" newTableName="evdnc_supportwork_actions"/>
        <renameTable oldTableName="supportthreatwork_actions" newTableName="evdnc_threatwork_actions"/>
        <renameTable oldTableName="supporthrwork_actions" newTableName="evdnc_hrwork_actions"/>
    </changeSet>
    <changeSet id="rename-evdnc-more-risk" author="adamjhamer">
        <!-- this appears to be a table about the riskName side of a supportaction -->
        <!-- the riskId in it is an actiongroups.id -->
        <renameTable oldTableName="supportplanrisks" newTableName="arch_evdnc_supportrisks"/>
    </changeSet>
    <changeSet id="archive-hrwork" author="adamjhamer">
        <renameTable oldTableName="evdnc_hrwork" newTableName="arch_evdnc_hrwork"/>
        <renameTable oldTableName="evdnc_hrcomments" newTableName="arch_evdnc_hrcomments"/>
        <renameTable oldTableName="evdnc_hractions" newTableName="arch_evdnc_hractions"/>
        <renameTable oldTableName="evdnc_hroutcomes" newTableName="arch_evdnc_hroutcomes"/>
        <renameTable oldTableName="evdnc_hrwork_actions" newTableName="arch_evdnc_hrwork_actions"/>
    </changeSet>

    <!-- undo anything we may have ran on 22.09 -->
    <changeSet id="DEV-2472-unify-sourcePage-form-undo" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <columnExists tableName="evdnc_form_work" columnName="sourcePage"/>
        </preConditions>
        <renameColumn tableName="evdnc_form_work" oldColumnName="sourcePage" newColumnName="evidenceTaskId" columnDataType="BIGINT"/>
    </changeSet>

    <changeSet id="DEV-2472-unify-sourcePage" author="adamjhamer">
        <renameColumn tableName="evdnc_form_work" oldColumnName="evidenceTaskId" newColumnName="taskDefId" columnDataType="BIGINT"/>
        <!-- TODO foreign key to taskdefinitions for support/threat! -->
        <renameColumn tableName="evdnc_supportwork" oldColumnName="sourcePage" newColumnName="taskDefId" columnDataType="BIGINT"/>
        <renameColumn tableName="evdnc_threatwork" oldColumnName="sourcePage" newColumnName="taskDefId" columnDataType="BIGINT"/>
    </changeSet>
    <changeSet id="DEV-2472-unify-sourcePageGroup" author="adamjhamer">
        <renameColumn tableName="evdnc_supportwork" oldColumnName="sourcePageGroup" newColumnName="evidenceGroupId" columnDataType="BIGINT"/>
        <renameColumn tableName="evdnc_threatwork" oldColumnName="sourcePageGroup" newColumnName="evidenceGroupId" columnDataType="BIGINT"/>
    </changeSet>
    <changeSet id="DEV-2472-unify-sourcePageGroup-forms" author="adamjhamer">
        <renameColumn tableName="evdnc_form_work" oldColumnName="evidenceTaskGroupKey" newColumnName="evidenceGroupKey" columnDataType="VARCHAR(20)"/>
    </changeSet>

    <!-- create the evidenceGroupId from the taskDefId - because custom forms know nothing else -->
    <changeSet id="DEV-2472-add-evidenceGroup-form" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="evdnc_form_work" columnName="evidenceGroupId"/>
            </not>
        </preConditions>
        <addColumn tableName="evdnc_form_work">
            <column name="evidenceGroupId" type="BIGINT" valueNumeric="taskDefId">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="DEV-2223-riskplanflags-workdate" author="adamjhamer">
        <addColumn tableName="evdnc_threatflags">
            <column name="workdate" type="DATETIME"/>
        </addColumn>
    </changeSet>

</databaseChangeLog>