package com.ecco.offline;

import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;

import java.util.Base64;

public class CachedResourceBuilder {
    private String path;
    private byte[] responseBody;
    private HttpHeaders headers = new HttpHeaders();

    CachedResourceBuilder(String path, byte[] responseBody) {
        Assert.notNull(path);
        Assert.notNull(responseBody);

        this.path = path;
        this.responseBody = responseBody;
    }

    public CachedResourceBuilder contentType(MediaType contentType) {
        headers.setContentType(contentType);
        return this;
    }

    public CachedResourceBuilder lastModified(long lastModified) {
        headers.setLastModified(lastModified);
        return this;
    }

    public CachedResourceBuilder maxAge(int numSeconds) {
        headers.setCacheControl("max-age=" + numSeconds);
        return this;
    }

    public CachedResourceBuilder mustRevalidate(int numSeconds) {
        headers.setCacheControl("must-revalidate, max-age=" + numSeconds);
        return this;
    }

    public CachedResource build() {
        return new CachedResource(path, buildResponseEntity());
    }

    public FallbackResource buildFallbackResource(String fallbackNamespace) {
        return new FallbackResource(fallbackNamespace, path, buildResponseEntity());
    }

    private ResponseEntity<byte[]> buildResponseEntity() {
        if (headers.getETag() == null) {
            headers.setETag("\"" + Base64.getEncoder().encodeToString(DigestUtils.sha256(responseBody)) + "\"");
        }

        return new ResponseEntity<>(responseBody, headers, HttpStatus.OK);
    }
}
