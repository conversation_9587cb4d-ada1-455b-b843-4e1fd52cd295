import BaseAsyncCommandForm = require("../cmd-queue/BaseAsyncCommandForm");
import Form = require("../controls/Form");
import InputGroup = require("../controls/InputGroup");
import TextInput = require("../controls/TextInput");
import commands = require("./commands");
import {apiClient} from "ecco-components";
import * as dto from "ecco-dto";
import {Individual} from "ecco-dto/contact-dto";
import {showFormInModalDom} from "../components/MUIConverterUtils";
import {ContactsAjaxRepository} from "ecco-dto";

var repository = new ContactsAjaxRepository(apiClient);


class EditContactForm extends BaseAsyncCommandForm<Individual> {

    public static showInModal(contactId: number) {
        var form = new EditContactForm(contactId);
        form.load();
        showFormInModalDom(form);
    }


    private form = new Form().addClass("form-30-50");

    private jobTitle = new TextInput("job title");
    private title = new TextInput("title");
    private firstName = new TextInput("first name");
    private lastName = new TextInput("last name");
    private mobileNumber = new TextInput("mobile");
    private phoneNumber = new TextInput("landline");
    private origContactDto: Individual;


    /** We provide the form with current state, and we get back a CommandQueue
     *  containing the changes to send/apply (if we click done).
     */
    constructor(private contactId: number) {
        super("Edit Contact");
        this.form
            .append( new InputGroup("job title", this.jobTitle) )
            .append( new InputGroup("title", this.title) )
            .append( new InputGroup("first name", this.firstName) )
            .append( new InputGroup("last name", this.lastName) )
            .append( new InputGroup("mobile", this.mobileNumber) )
            .append( new InputGroup("landline", this.phoneNumber) );
    }

    protected fetchViewData(): Promise<Individual> {
        return repository.findOneContact(this.contactId)
            .then(contact => {
                if (dto.isIndividual(contact)) {
                    return contact;
                } else {
                    throw new TypeError("Contact is not an Individual.");
                }
            });
    }

    protected render(contactDto: Individual) {
        this.origContactDto = contactDto;

        if (contactDto) {
            this.jobTitle.setVal(contactDto.jobTitle);
            this.title.setVal(contactDto.title);
            this.lastName.setVal(contactDto.lastName);
            this.firstName.setVal(contactDto.firstName);
            this.mobileNumber.setVal(contactDto.mobileNumber);
            this.phoneNumber.setVal(contactDto.phoneNumber);
        }

        this.enableSubmit(); // probably want to have this be linked to length of commandQueue (based on event?)
        this.element().empty();
        this.append(this.form);
    }


    protected override submitForm(): Promise<void> {
        var cmd;
        if (this.origContactDto) {
            cmd = new commands.ContactChangeCommand("update", this.origContactDto.contactId)
                .changeJobTitle(this.origContactDto.jobTitle, this.jobTitle.val())
                .changeTitle(this.origContactDto.title, this.title.val())
                .changeFirstName(this.origContactDto.firstName, this.firstName.val())
                .changeLastName(this.origContactDto.lastName, this.lastName.val())
                .changeMobileNumber(this.origContactDto.mobileNumber, this.mobileNumber.val())
                .changePhoneNumber(this.origContactDto.phoneNumber, this.phoneNumber.val());
        }
        else {
            cmd = new commands.ContactChangeCommand("add", this.origContactDto.contactId)
                .changeJobTitle(null, this.jobTitle.val())
                .changeTitle(null, this.title.val())
                .changeFirstName(null, this.firstName.val())
                .changeLastName(null, this.lastName.val())
                .changeMobileNumber(null, this.mobileNumber.val())
                .changePhoneNumber(null, this.phoneNumber.val());
        }

        if (cmd.hasChanges()) {
            this.commandQueue.addCommand(cmd);
        }
        return super.submitForm();
    }
}

export = EditContactForm;
