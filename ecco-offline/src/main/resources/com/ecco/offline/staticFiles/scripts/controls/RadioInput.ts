import $ = require("jquery");
import BaseControl = require("./BaseControl");
import InputControl = require("./InputControl");

class RadioInput extends BaseControl implements InputControl {

    private onChange: (value: string) => void;
    private $input: $.JQuery;

    public constructor(private name: string, private label: string, private value: string, id?: string, className: string = "radio") {
        super($("<span>", id));
        id = id || label; // default to label.
        this.$input = $('<input>', {'class': 'radio', type: 'radio', name: name, id: id, value: value});
        var $lbl = $('<label>', {'for': id}).append(this.$input).append(label);
        this.element().addClass(className).append($lbl);
    }

    public change( onChange: (value: string) => void ): this {
        if (!onChange) return this;

        this.onChange = onChange;

        this.$input.change( (event: $.JQueryEventObject) => {
            var val = $(event.delegateTarget).val();
            this.onChange(val);
        });

        return this;
    }

    public setChecked(isChecked: boolean) {
        this.$input.prop('checked', isChecked);
        return this;
    }

    public isChecked(): boolean {
        return this.$input.is(':checked')
    }

    public val(): boolean {
        return this.$input.val();
    }
}

export = RadioInput;
