import $ = require("jquery");
import ListDefSelectList = require("./ListDefSelectList");
import ListDefHierarchicalSharedValueSelectList = require("./ListDefHierarchicalSharedValueSelectList");
import {SessionDataRepository} from "ecco-dto";


/**
 * Hierarchical select lists which creates a new select control as choices are made. This allows a more flexible hierarchy
 * of values to be used - see listdef-hierarchical-auto-test. This assumes each selected item has only one child list (linear hierarchy).
 */
class ListDefHierarchicalSharedValueAutoSelectList extends ListDefHierarchicalSharedValueSelectList {

    constructor(selector: $.JQuery, protected override featureConfigRepository: SessionDataRepository, private update?: ((val: string) => void) | undefined) {
        super(selector, featureConfigRepository);
    }

    // override initial value could avoid using the dom
    /*protected calculateInitialValue(list: ListDefSelectList): string {
        return this.getSharedElementIdFromList(list); //$container.data("initial-value-shared");
    }*/

    /**
     * It seems this class was only about an initial load, no re-rendering expected.
     * So, we set the initial list value, and rerender them.
     * However, the issue was in fact in the superclass
     */
/*    public setInitialValueAndProcess(val: number) {
        const initialList = this.lists[0];
        //initialList.setInitialValue(val);
        const sharedElementId = this.getSharedElementIdFromList(initialList);
        $('#'+sharedElementId).data("initial-value-shared", val);

        // re-create the elements
        this.processNextListToInitialValue(0);
    }*/

    protected override loadAll() {
        super.loadAll();
        this.processNextListToInitialValue(0);
    }

    private processNextListToInitialValue(count: number) {
        var initialValueForList = this.calculateInitialValue(this.lists[count]);
        if (initialValueForList) {
            this.processNextList(parseInt(initialValueForList), this.lists[count]);
            this.processNextListToInitialValue(count+1);
        }
    }

    protected override postPopulate(newValue: string, changedList: ListDefSelectList, lastList: ListDefSelectList) {
        super.postPopulate(newValue, changedList, lastList);
        this.update && this.update(newValue)
        this.processNextList(parseInt(newValue), changedList);
    }

    private processNextList(idSelected: number, changedList: ListDefSelectList) {
        this.removeChildrenOf(changedList);

        var childListName = this.getBestGuessChildListName(idSelected);
        if (!childListName) {
            return;
        }

        var sharedName = this.getSharedElementIdFromList(changedList);
        var initialValueShared = changedList.element().data("initial-value-shared");
        var $container = $("<div>");
        $container.data("name-shared", sharedName);
        $container.data("initial-value-shared", initialValueShared);
        $container.attr("list-name", childListName);
        changedList.element().after($container);

        var list = this.createList($container);
        this.loadList(changedList.getListName(), list);
        this.createSharedElement(list);
        this.populateAllChildrenFromList(changedList);
    }

}
export = ListDefHierarchicalSharedValueAutoSelectList;
