import $ = require("jquery");
import BaseAsyncDataControl = require("../../controls/BaseAsyncDataControl");
import {ReloadEvent} from "@eccosolutions/ecco-common";
import {
    apiClient,
    flagStyledAsGraphical
} from "ecco-components";
import {reactElementContainer} from "ecco-components-core";

import {
    FlagArea,
    FlagEvidenceDto,
    ReferralAjaxRepository, ReferralSummaryDto,
    RiskEvidenceAjaxRepository, ServiceRecipientAjaxRepository, ServiceType,
    SessionData, SupportWork,
    SupportWorkAjaxRepository
} from "ecco-dto";
import * as dtoSupport from "ecco-dto/evidence-dto";
import * as dto from "ecco-dto/evidence-risk-dto";
import {SessionDataService} from "../../feature-config/SessionDataService";
import {ServiceRecipient} from "ecco-dto/service-recipient-dto";
import {bus} from "@eccosolutions/ecco-common";

const lazyEvidenceDelegatingForm = () => import("../EvidenceDelegatingForm");

const serviceRecipientAjaxRepository = new ServiceRecipientAjaxRepository(apiClient);
const supportWorkAjaxRepository = new SupportWorkAjaxRepository(apiClient);
const riskEvidenceAjaxRepository = new RiskEvidenceAjaxRepository(apiClient);
const referralAjaxRepository = new ReferralAjaxRepository(apiClient);

// see useWorkflow / useReloadHandler / useEventHandler for how to use events in react hooks
// for react components, see the comments on this commit, and search bus.addHandler
export class FlagReloadEvent {
    public static bus = bus<FlagReloadEvent>();
}

class BackingData {
    constructor(public sessionData: SessionData,
                public serviceRecipient: ServiceRecipient,
                public maybeReferralSummary: ReferralSummaryDto,
                public supportWorkRiskOutstanding: dtoSupport.SupportWork[],
                public flagsRiskSnapshot: dto.RiskFlagsSnapshotDto | null,
                public flagAreas: dtoSupport.FlagArea[]) {}
}

const hrLine = () => $("<hr>").css({"margin": "10px 0px 0px 0px", "height":"1px","border":"none","color":"#999","background-color":"#999"});

/**
 * Cover the things to show in the status area. NB This does NOT include hact for now. See referralViewStatusBarNew.jsp.
 */
export class RiskStatusAreaControl extends BaseAsyncDataControl<BackingData> {

    private riskTaskName: string;

    constructor(private serviceRecipientId: number, private serviceRecipient?: ServiceRecipient | undefined, private showFileContext?: boolean | undefined, private dummyReload?: number | undefined) {
        super();
        // we're already wired on a reload event, but this is possibly overkill and we're trying to be more specific
        // it also doesn't have a destroy function
        ReloadEvent.bus.addHandler(() => this.load());
    }

    protected fetchViewData(): Promise<BackingData> {

        const sessionDataQ = SessionDataService.getFeatures();
        const serviceRecipientAjaxRepositoryQ = this.serviceRecipient
                ? Promise.resolve(this.serviceRecipient)
                : serviceRecipientAjaxRepository.findOneServiceRecipientById(this.serviceRecipientId);

        // get all flags (risk flags and evidence flags) for a sr
        return sessionDataQ.then(sessionData => {
            return serviceRecipientAjaxRepositoryQ.then(sr => {

                const serviceType = sessionData.getServiceTypeByServiceCategorisationId(sr.serviceAllocationId);
                //const riskManagementName = items.serviceRecipient.features.getRiskManagementTaskForServiceTypeId(this.serviceRecipient.configResolver.getServiceType().getId());
                this.riskTaskName = serviceType.getFirstRiskTaskName(sessionData);

                // if we have loaded to the sr, then we have permission to load the referral
                // but we use UnsecuredAcl to avoid scope client hitting unauth issues
                const maybeReferralQ: Promise<ReferralSummaryDto> = sr.prefix == "r"
                        ? referralAjaxRepository.findAllUnsecuredAclReferralSummaryByServiceRecipientId([sr.serviceRecipientId]).then(allR => allR[0])
                        : Promise.resolve(null);

                const supportWorkRiskOutstandingQ: Promise<SupportWork[]> = this.hasRiskRequired(sessionData.getServiceTypeByServiceCategorisationId(sr.serviceAllocationId))
                        ? supportWorkAjaxRepository.findSupportWorkByServiceRecipientIdAndRiskManagementOutstanding(sr.serviceRecipientId)
                        : Promise.resolve([]);

                let flagRiskSnapshotQ: Promise<dto.RiskFlagsSnapshotDto | null> =
                    riskEvidenceAjaxRepository.findRiskFlagsSnapshotByServiceRecipientIdAndEvidenceGroup(sr.serviceRecipientId)

                // non-risk flags - questionnaires with 'showFlagsOn' 'statusArea', eg saoca
                // nb we've taken out support from having this , just because it never seemed to be a real option
                let flagSnapshotsQ = serviceRecipientAjaxRepository.fetchStatusAreaEvidenceFlags(sessionData, sr.serviceRecipientId, sessionData.getServiceTypeByServiceCategorisationId(sr.serviceAllocationId));

                return Promise.all([sessionDataQ, serviceRecipientAjaxRepositoryQ, maybeReferralQ, supportWorkRiskOutstandingQ, flagRiskSnapshotQ, flagSnapshotsQ])
                    .then(([sessionData, sr, maybeReferral, supportWorkRiskOutstanding, riskFlagSnapshot, flagSnapshots]) => {
                        return new BackingData(sessionData, sr, maybeReferral, supportWorkRiskOutstanding, riskFlagSnapshot, flagSnapshots);
                    });
            })
        })
    }

    private hasRiskRequired(serviceType: ServiceType) {
        const hasRiskRequired = serviceType.getTaskDefinitionEntries()
                .some(t => t.hasSetting("showCommentComponents", "showRiskManagementRequired"))
        return hasRiskRequired;
    }

    protected render(items: BackingData): void {
        this.element().empty();

        const $forDeletion = this.markedForDeletion(items);
        this.element().append($forDeletion);

        const showContext = this.showFileContext && items.maybeReferralSummary;
        const hasFlags = this.getRiskFlagsOn(items).length > 0 || this.getSupportFlagsOn(items).length > 0;
        if (showContext && hasFlags) {
            const serviceDesc = items.sessionData.getServiceCategorisationName(items.serviceRecipient.serviceAllocationId);
            this.element().append(hrLine());
            this.element().append($("<span>")
                    .css({"font-size": "small", "margin-top": "10px"})
                    .text(`r-id: ${items.maybeReferralSummary.referralCode || items.maybeReferralSummary.referralId} (${serviceDesc})   `)
            );
        }

        if (this.riskTaskName) {
            const $p = this.riskManagementRequiredOn(items);
            const $flags = this.renderRiskFlags(items);
            if ($p != null) {
                this.element().append($p);
            }
            this.element().append($("<div>").append($flags));
        }

        const $flagsSupport = this.renderSupportFlags(items);
        this.element().append($("<div>").append($flagsSupport));

        if (showContext && hasFlags) {
            this.element().append(hrLine());
        }

    }

    private markedForDeletion(items: BackingData) {
        const onlyThisFile = this.showFileContext != null ? !this.showFileContext : true;
        return onlyThisFile && items.maybeReferralSummary?.requestedDelete
                ? $("<div>").addClass("alert alert-warning").text("this file is marked for deletion!")
                : null;
    }

    private getRiskFlagsOn(data: BackingData) {
        let flagsSet: FlagEvidenceDto[] = [];
        data.flagsRiskSnapshot && data.flagsRiskSnapshot.latestFlags
                .filter(flag => flag.value == true)
                .forEach(f => flagsSet.push(f));
        return flagsSet;
    }

    private getSupportFlagsOn(data: BackingData, flagArea?: FlagArea | undefined) {
        const flagsSet: FlagEvidenceDto[] = [];
        const flagsRelevant = data.flagAreas
                .filter(fa => !flagArea ? true : fa.evidenceDef.getTaskName() == flagArea.evidenceDef.getTaskName())
                .map(fa => fa.flagSnapshots)
                .reduce((r, x) => r.concat(x), []) // flatMap
        ;
        flagsRelevant
                .filter(flag => flag.value == true)
                .forEach(f => flagsSet.push(f));
        return flagsSet;
    }

    private renderRiskFlags(data: BackingData) {
        const $flagsArea = $("<span>");
        let $flagsAreaContent = $("<span>");

        const flagsSet = this.getRiskFlagsOn(data);
        if (flagsSet.length > 0) {
            flagsSet.forEach((flag, i) => {
                let {label, $flagSrc} = RiskStatusAreaControl.renderFlag(data, flag, i, flagsSet);
                const $label = $("<span>").text(label);
                if (this.showFileContext) {
                    $label.css({"font-size": "small"});
                }
                $flagsAreaContent.append($flagSrc).append($label);
            });
        }

        $flagsArea.append($flagsAreaContent);
        return $flagsArea;
    }

    private renderSupportFlags(data: BackingData) {
        const showContext = this.showFileContext && data.maybeReferralSummary;

        const $flagsArea = $("<span>");
        // NB we still need all the areas to display without any flags, as that is our link
        data.flagAreas && data.flagAreas.forEach(fa => {
            $flagsArea.append(this.renderSupportFlagEvidenceTask(showContext, data, fa));
        });
        return $flagsArea;
    }

    private renderSupportFlagEvidenceTask(showContext: ReferralSummaryDto, data: BackingData, flagArea: FlagArea) {
        let $flags = $("<span>");

        // don't show the link if we are a context
        if (!this.showFileContext) {
            const $evdPage = $("<a>").addClass("no-print").text(`${flagArea.label}: `)
                    .click(() => {
                        lazyEvidenceDelegatingForm().then(({EvidenceDelegatingForm}) => {
                            EvidenceDelegatingForm.showInModalByIds(data.serviceRecipient.serviceRecipientId, "", flagArea.evidenceDef.getTaskName());
                        });
                    });
            $flags.append($evdPage);
        }

        let flagsSet: FlagEvidenceDto[] = this.getSupportFlagsOn(data, flagArea);
        if (!this.showFileContext && flagsSet.length == 0) {
            $flags.append($("<span>").text("-"));
        }
        flagsSet.forEach((fs, i) => {
            let {label, $flagSrc} = RiskStatusAreaControl.renderFlag(data, fs, i, flagsSet);
            const $label = $("<span>").text(label);
            if (this.showFileContext) {
                $label.css({"font-size": "small"});
            }
            $flags.append($flagSrc).append($label);
        })
        return $flags;
    }

    // also see flags.tsx flagsAsGraphical
    private static renderFlag(data: BackingData, flag: FlagEvidenceDto, i: number, flagsSet: FlagEvidenceDto[]) {
        const flagDefId = data.sessionData.getListDefinitionEntryById(flag.flagId);
        const flagName = flagDefId.getName();
        let label = ((i + 1) < flagsSet.length)
                ? flagName.concat(", ")
                : flagName;

        // as per EvidenceCommentForm
        let $flagMount: $.JQuery | undefined = undefined;
        const el = flagStyledAsGraphical(data.sessionData, flag, false);
        if (el) {
            // have a small margin if we have no styling
            $flagMount = $("<span>").css("margin-right", "5px");
            reactElementContainer(el, $flagMount[0]);
        }

        let $flagSrc = el
                ? $flagMount
                : $("<span>")
                        .attr("role", "img")
                        .addClass("std-flag-image")
                        .addClass("flag_red24");
        return {label, $flagSrc};
    }

    private riskManagementRequiredOn(items: BackingData) {
        const hasRiskRequired = this.hasRiskRequired(items.sessionData.getServiceTypeByServiceCategorisationId(items.serviceRecipient.serviceAllocationId));

        if (!hasRiskRequired) {
            return null;
        }

        const $p = $("<p>").text("risk management required on: ");

        if (items.supportWorkRiskOutstanding.length == 0) {
            $p.append("0");
            // avoid printing if we have nothing
            // also see EvidenceCommentForm#showRiskManagementRequired for feature toggle, but more tricky here
            return null;
        } else {
            $p.append($("<a>").text(items.supportWorkRiskOutstanding.length)
                    .click(() => {
                        lazyEvidenceDelegatingForm().then(({EvidenceDelegatingForm}) => {
                            EvidenceDelegatingForm.showInModalByIds(items.serviceRecipient.serviceRecipientId, "", this.riskTaskName);
                        });
                    }));
        }
        return $p;
    }
}
