
import ActionNode = require("./ActionNode");
import * as domain from "ecco-dto";
import dynamicTree = require("../../draw/dynamic-tree");
import * as evidenceDto from "ecco-dto/evidence-dto";
import GraphContext = require("./GraphContext");
import SupportAction = evidenceDto.SupportAction;
import Node = dynamicTree.DynamicTreeNode;
import NodeProxy = require("./NodeProxy");
import { showInModalDom } from 'ecco-components-core';

class ActionGroupNode implements NodeProxy {

    private readonly node: Node;
    private actionNodesById = new Map<string, ActionNode>();

    constructor(private actionGroup: domain.ActionGroup,
                private supportAction: SupportAction,
                private context: GraphContext) {
        this.node = new Node(actionGroup.getName());
        this.node.addClickEventHandler( () => { this.clicked();} );
    }

    private clicked() {
        // FIXME: Modal should be able to be private attr and reused, but show/hide/show means click handlers don't work!
        const content = document.createElement("p");
        content.textContent = "Work in progress...";
        showInModalDom(`Actions for ${this.actionGroup.getName()}`, content);
    }

    public addAction(action: domain.Action, supportAction: SupportAction): void {
        this.getActionNode(action, supportAction);
    }

    /** Get ActionNode for supplied def.  Create it if it didn't exist.
     */
    public getActionNode(action: domain.Action, supportAction: SupportAction) {
        const id = supportAction.actionInstanceUuid;
        let node = this.actionNodesById.get(id);
        if (!node) {
            node = new ActionNode(action, supportAction, this.context);
            this.node.addChild(node.getNode());
            this.actionNodesById.set(id, node);
        }
        return node;
    }

    public withAllLeafNodes( callback: (leaf: ActionNode) => void): void {
        for (const node of this.actionNodesById.values()) {
            node.withAllLeafNodes( callback );
        }
    }

    public getNode(): Node {
        return this.node;
    }
}
export = ActionGroupNode;