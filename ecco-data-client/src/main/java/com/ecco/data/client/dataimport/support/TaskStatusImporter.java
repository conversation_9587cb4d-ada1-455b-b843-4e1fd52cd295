package com.ecco.data.client.dataimport.support;

import com.ecco.data.client.dataimport.csv.CSVBeanReader;
import com.ecco.data.client.model.TaskStatusImportViewModel;
import com.google.common.base.Throwables;
import com.google.common.collect.ImmutableMap;
import com.google.common.io.CharSource;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.util.Map;

public class TaskStatusImporter {

    protected final Map<String, String> fieldAliases = ImmutableMap.<String,String>builder()
            .build();

    protected final RestTemplate restTemplate;

    public TaskStatusImporter(RestTemplate template) {
        this.restTemplate = template;
    }

    public void read(CharSource source) {

      final Map<String, String> allSynonyms = ImmutableMap.<String,String>builder().putAll(Synonyms.synonymsToNormalizedMap).build();
      try {
        new CSVBeanReader<>(source, TaskStatusImportViewModel.class, fieldAliases, allSynonyms)
                .readUsing(new TaskStatusHandler(restTemplate));
      } catch (IOException e) {
        throw Throwables.propagate(e);
      }
    }

}
