package com.ecco.data.client.dataimport.support;

import com.ecco.data.client.model.evidence.GoalImportViewModel;
import com.ecco.dom.EvidenceGroup;
import com.ecco.dto.ChangeViewModel;
import com.ecco.evidence.EvidenceTask;
import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.ecco.webApi.evidence.GoalUpdateCommandViewModel;
import com.ecco.webApi.evidence.SupportSmartStepsSnapshotViewModel;
import com.ecco.webApi.featureConfig.SessionDataViewModel;
import org.joda.time.DateTime;
import org.springframework.web.client.RestTemplate;

import java.util.UUID;

public class SupportGoalEvidenceHandler extends AbstractHandler<GoalImportViewModel> {

    private SessionDataViewModel sessionData;

    public SupportGoalEvidenceHandler(RestTemplate restTemplate) {
        super(restTemplate);
    }

    @Override
    protected void processEntity(ImportOperation<GoalImportViewModel> operation) {

        var input = operation.record;
        //var rvm = getReferralById(operation, input.referralId);
        var rvm = getReferralById(operation, input.referralId);

        // set sessionData as a class level variable if not already set
        if (sessionData == null) {
            sessionData = sessionDataActor.getSessionData().getBody();
        }

        var now = new DateTime();
        SupportSmartStepsSnapshotViewModel latestSnapshot = supportEvidenceActor.findTimestampSupportSnapshot(rvm.serviceRecipientId,
                EvidenceGroup.NEEDS, now, null).getBody();

        if (latestSnapshot != null) {
            var actionMaybe = latestSnapshot.latestActions.stream().filter(a -> a.actionInstanceUuid.equals(input.actionInstanceUuid)).findFirst();

            if (actionMaybe.isPresent()) {
                var action = actionMaybe.get();
                UUID workUuid = UUID.randomUUID();

                if (action.targetDateTime != null && input.targetDate != null && !action.targetDateTime.toLocalDate().equals(input.targetDate.toLocalDate())) {
                    GoalUpdateCommandViewModel goal = new GoalUpdateCommandViewModel(workUuid,
                            rvm.serviceRecipientId,
                            EvidenceGroup.NEEDS,
                            new EvidenceTask(input.evidenceTask),
                            input.actionDefId,
                            input.actionInstanceUuid,
                            null);

                    goal.operation = BaseCommandViewModel.OPERATION_UPDATE;
                    goal.targetDateChange = ChangeViewModel.create(action.targetDateTime.toLocalDate(), input.targetDate.toLocalDate());

                    syncGoalCommandToServer(operation.baseUri.concat(apiPath), goal);
                }
            }
        }
    }

    // use a command, rather than an actor - just because...
    private long syncGoalCommandToServer(String uri, GoalUpdateCommandViewModel input) {
        com.ecco.webApi.viewModels.Result result = executeCommand(uri, input);
        return Long.parseLong(result.getId());
    }

}
